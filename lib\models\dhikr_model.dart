import 'package:flutter/material.dart';

class DhikrModel {
  final String text;
  final String translation;
  final Color color;
  final Color particleColor;
  final int count;
  final DateTime? lastUsed;

  DhikrModel({
    required this.text,
    required this.translation,
    required this.color,
    required this.particleColor,
    this.count = 0,
    this.lastUsed,
  });

  DhikrModel copyWith({
    String? text,
    String? translation,
    Color? color,
    Color? particleColor,
    int? count,
    DateTime? lastUsed,
  }) {
    return DhikrModel(
      text: text ?? this.text,
      translation: translation ?? this.translation,
      color: color ?? this.color,
      particleColor: particleColor ?? this.particleColor,
      count: count ?? this.count,
      lastUsed: lastUsed ?? this.lastUsed,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'translation': translation,
      'color': color.value,
      'particleColor': particleColor.value,
      'count': count,
      'lastUsed': lastUsed?.millisecondsSinceEpoch,
    };
  }

  factory DhikrModel.fromJson(Map<String, dynamic> json) {
    return DhikrModel(
      text: json['text'],
      translation: json['translation'],
      color: Color(json['color']),
      particleColor: Color(json['particleColor']),
      count: json['count'] ?? 0,
      lastUsed: json['lastUsed'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['lastUsed'])
          : null,
    );
  }
}

class Achievement {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final int targetCount;
  final bool isUnlocked;
  final DateTime? unlockedAt;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.targetCount,
    this.isUnlocked = false,
    this.unlockedAt,
  });

  Achievement copyWith({
    String? id,
    String? title,
    String? description,
    IconData? icon,
    Color? color,
    int? targetCount,
    bool? isUnlocked,
    DateTime? unlockedAt,
  }) {
    return Achievement(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      targetCount: targetCount ?? this.targetCount,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      unlockedAt: unlockedAt ?? this.unlockedAt,
    );
  }
}

class DailyStats {
  final DateTime date;
  final int totalCount;
  final Map<String, int> dhikrCounts;
  final Duration timeSpent;

  DailyStats({
    required this.date,
    required this.totalCount,
    required this.dhikrCounts,
    required this.timeSpent,
  });

  Map<String, dynamic> toJson() {
    return {
      'date': date.millisecondsSinceEpoch,
      'totalCount': totalCount,
      'dhikrCounts': dhikrCounts,
      'timeSpent': timeSpent.inMilliseconds,
    };
  }

  factory DailyStats.fromJson(Map<String, dynamic> json) {
    return DailyStats(
      date: DateTime.fromMillisecondsSinceEpoch(json['date']),
      totalCount: json['totalCount'],
      dhikrCounts: Map<String, int>.from(json['dhikrCounts']),
      timeSpent: Duration(milliseconds: json['timeSpent']),
    );
  }
}
