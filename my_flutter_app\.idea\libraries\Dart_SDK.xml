<component name="libraryTable">
  <library name="Dart SDK">
    <CLASSES>
      <root url="file://C:\flutter/bin/cache/dart-sdk/lib/async" />
      <root url="file://C:\flutter/bin/cache/dart-sdk/lib/collection" />
      <root url="file://C:\flutter/bin/cache/dart-sdk/lib/convert" />
      <root url="file://C:\flutter/bin/cache/dart-sdk/lib/core" />
      <root url="file://C:\flutter/bin/cache/dart-sdk/lib/developer" />
      <root url="file://C:\flutter/bin/cache/dart-sdk/lib/html" />
      <root url="file://C:\flutter/bin/cache/dart-sdk/lib/io" />
      <root url="file://C:\flutter/bin/cache/dart-sdk/lib/isolate" />
      <root url="file://C:\flutter/bin/cache/dart-sdk/lib/math" />
      <root url="file://C:\flutter/bin/cache/dart-sdk/lib/mirrors" />
      <root url="file://C:\flutter/bin/cache/dart-sdk/lib/typed_data" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>