import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/dhikr_model.dart';

class Stats<PERSON>hart extends StatefulWidget {
  final List<DailyStats> weeklyStats;

  const StatsChart({
    super.key,
    required this.weeklyStats,
  });

  @override
  State<StatsChart> createState() => _StatsChartState();
}

class _StatsChartState extends State<StatsChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          painter: ChartPainter(
            weeklyStats: widget.weeklyStats,
            animation: _animation.value,
          ),
          size: const Size(double.infinity, 200),
        );
      },
    );
  }
}

class ChartPainter extends CustomPainter {
  final List<DailyStats> weeklyStats;
  final double animation;

  ChartPainter({
    required this.weeklyStats,
    required this.animation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (weeklyStats.isEmpty) return;

    final paint = Paint()
      ..color = const Color(0xFF2E7D32)
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final fillPaint = Paint()
      ..color = const Color(0xFF2E7D32).withOpacity(0.2)
      ..style = PaintingStyle.fill;

    final gridPaint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..strokeWidth = 1;

    final textPainter = TextPainter(
      textDirection: TextDirection.rtl,
    );

    // Calculate dimensions
    final chartWidth = size.width - 60;
    final chartHeight = size.height - 60;
    final chartLeft = 40.0;
    final chartTop = 20.0;

    // Find max value for scaling
    final maxValue = weeklyStats
        .map((stat) => stat.totalCount)
        .reduce(math.max)
        .toDouble();

    // Draw grid lines
    _drawGrid(canvas, size, gridPaint, textPainter, maxValue, chartLeft, chartTop, chartWidth, chartHeight);

    // Draw chart
    _drawChart(canvas, size, paint, fillPaint, chartLeft, chartTop, chartWidth, chartHeight, maxValue);

    // Draw data points
    _drawDataPoints(canvas, size, chartLeft, chartTop, chartWidth, chartHeight, maxValue);

    // Draw labels
    _drawLabels(canvas, size, textPainter, chartLeft, chartTop, chartWidth, chartHeight);
  }

  void _drawGrid(Canvas canvas, Size size, Paint gridPaint, TextPainter textPainter,
      double maxValue, double chartLeft, double chartTop, double chartWidth, double chartHeight) {
    
    // Horizontal grid lines
    for (int i = 0; i <= 5; i++) {
      final y = chartTop + (chartHeight * i / 5);
      canvas.drawLine(
        Offset(chartLeft, y),
        Offset(chartLeft + chartWidth, y),
        gridPaint,
      );

      // Y-axis labels
      final value = (maxValue * (5 - i) / 5).round();
      textPainter.text = TextSpan(
        text: value.toString(),
        style: const TextStyle(
          color: Colors.grey,
          fontSize: 12,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(chartLeft - textPainter.width - 8, y - textPainter.height / 2),
      );
    }

    // Vertical grid lines
    for (int i = 0; i < weeklyStats.length; i++) {
      final x = chartLeft + (chartWidth * i / (weeklyStats.length - 1));
      canvas.drawLine(
        Offset(x, chartTop),
        Offset(x, chartTop + chartHeight),
        gridPaint,
      );
    }
  }

  void _drawChart(Canvas canvas, Size size, Paint paint, Paint fillPaint,
      double chartLeft, double chartTop, double chartWidth, double chartHeight, double maxValue) {
    
    final path = Path();
    final fillPath = Path();
    
    for (int i = 0; i < weeklyStats.length; i++) {
      final x = chartLeft + (chartWidth * i / (weeklyStats.length - 1));
      final normalizedValue = weeklyStats[i].totalCount / maxValue;
      final y = chartTop + chartHeight - (chartHeight * normalizedValue * animation);

      if (i == 0) {
        path.moveTo(x, y);
        fillPath.moveTo(x, chartTop + chartHeight);
        fillPath.lineTo(x, y);
      } else {
        path.lineTo(x, y);
        fillPath.lineTo(x, y);
      }
    }

    // Complete fill path
    fillPath.lineTo(chartLeft + chartWidth, chartTop + chartHeight);
    fillPath.close();

    // Draw fill
    canvas.drawPath(fillPath, fillPaint);

    // Draw line
    canvas.drawPath(path, paint);
  }

  void _drawDataPoints(Canvas canvas, Size size, double chartLeft, double chartTop,
      double chartWidth, double chartHeight, double maxValue) {
    
    final pointPaint = Paint()
      ..color = const Color(0xFF2E7D32)
      ..style = PaintingStyle.fill;

    final pointBorderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    for (int i = 0; i < weeklyStats.length; i++) {
      final x = chartLeft + (chartWidth * i / (weeklyStats.length - 1));
      final normalizedValue = weeklyStats[i].totalCount / maxValue;
      final y = chartTop + chartHeight - (chartHeight * normalizedValue * animation);

      // Draw point border
      canvas.drawCircle(Offset(x, y), 6, pointBorderPaint);
      
      // Draw point
      canvas.drawCircle(Offset(x, y), 4, pointPaint);
    }
  }

  void _drawLabels(Canvas canvas, Size size, TextPainter textPainter,
      double chartLeft, double chartTop, double chartWidth, double chartHeight) {
    
    final dayNames = ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];
    
    for (int i = 0; i < weeklyStats.length; i++) {
      final x = chartLeft + (chartWidth * i / (weeklyStats.length - 1));
      final dayIndex = weeklyStats[i].date.weekday % 7;
      
      textPainter.text = TextSpan(
        text: dayNames[dayIndex],
        style: const TextStyle(
          color: Colors.grey,
          fontSize: 10,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, chartTop + chartHeight + 8),
      );
    }
  }

  @override
  bool shouldRepaint(ChartPainter oldDelegate) {
    return animation != oldDelegate.animation ||
           weeklyStats != oldDelegate.weeklyStats;
  }
}

class CircularProgressChart extends StatefulWidget {
  final double progress;
  final Color color;
  final String label;
  final String value;

  const CircularProgressChart({
    super.key,
    required this.progress,
    required this.color,
    required this.label,
    required this.value,
  });

  @override
  State<CircularProgressChart> createState() => _CircularProgressChartState();
}

class _CircularProgressChartState extends State<CircularProgressChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          painter: CircularChartPainter(
            progress: _animation.value,
            color: widget.color,
          ),
          child: Container(
            width: 120,
            height: 120,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    widget.value,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: widget.color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.label,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class CircularChartPainter extends CustomPainter {
  final double progress;
  final Color color;

  CircularChartPainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2 - 10;

    // Background circle
    final backgroundPaint = Paint()
      ..color = color.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 8;

    canvas.drawCircle(center, radius, backgroundPaint);

    // Progress arc
    final progressPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 8
      ..strokeCap = StrokeCap.round;

    final sweepAngle = 2 * math.pi * progress;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2,
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(CircularChartPainter oldDelegate) {
    return progress != oldDelegate.progress;
  }
}

class BarChart extends StatefulWidget {
  final Map<String, int> data;
  final Map<String, Color> colors;

  const BarChart({
    super.key,
    required this.data,
    required this.colors,
  });

  @override
  State<BarChart> createState() => _BarChartState();
}

class _BarChartState extends State<BarChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          painter: BarChartPainter(
            data: widget.data,
            colors: widget.colors,
            animation: _animation.value,
          ),
          size: const Size(double.infinity, 200),
        );
      },
    );
  }
}

class BarChartPainter extends CustomPainter {
  final Map<String, int> data;
  final Map<String, Color> colors;
  final double animation;

  BarChartPainter({
    required this.data,
    required this.colors,
    required this.animation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final maxValue = data.values.reduce(math.max).toDouble();
    final barWidth = (size.width - 40) / data.length - 20;
    final chartHeight = size.height - 60;

    final textPainter = TextPainter(
      textDirection: TextDirection.rtl,
    );

    int index = 0;
    data.forEach((key, value) {
      final x = 20 + (index * (barWidth + 20));
      final normalizedValue = value / maxValue;
      final barHeight = chartHeight * normalizedValue * animation;
      final y = size.height - 40 - barHeight;

      // Draw bar
      final barPaint = Paint()
        ..color = colors[key] ?? Colors.blue
        ..style = PaintingStyle.fill;

      final rect = RRect.fromRectAndRadius(
        Rect.fromLTWH(x, y, barWidth, barHeight),
        const Radius.circular(4),
      );
      canvas.drawRRect(rect, barPaint);

      // Draw value label
      textPainter.text = TextSpan(
        text: value.toString(),
        style: const TextStyle(
          color: Colors.black,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x + barWidth / 2 - textPainter.width / 2, y - 20),
      );

      // Draw category label
      textPainter.text = TextSpan(
        text: key.split(' ')[0], // First word only
        style: const TextStyle(
          color: Colors.grey,
          fontSize: 10,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x + barWidth / 2 - textPainter.width / 2, size.height - 30),
      );

      index++;
    });
  }

  @override
  bool shouldRepaint(BarChartPainter oldDelegate) {
    return animation != oldDelegate.animation ||
           data != oldDelegate.data;
  }
}
