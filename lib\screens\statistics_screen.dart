import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/dhikr_model.dart';
import '../widgets/achievement_card.dart';
import '../widgets/stats_chart.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _totalCount = 0;
  int _todayCount = 0;
  int _weekCount = 0;
  int _monthCount = 0;
  List<Achievement> _achievements = [];
  List<DailyStats> _weeklyStats = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadStatistics();
    _initializeAchievements();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadStatistics() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _totalCount = prefs.getInt('total_counter') ?? 0;
      _todayCount = prefs.getInt('today_counter') ?? 0;
      _weekCount = prefs.getInt('week_counter') ?? 0;
      _monthCount = prefs.getInt('month_counter') ?? 0;
    });
    
    // Load weekly stats (mock data for now)
    _loadWeeklyStats();
  }

  void _loadWeeklyStats() {
    // Generate mock weekly data
    final now = DateTime.now();
    _weeklyStats = List.generate(7, (index) {
      final date = now.subtract(Duration(days: 6 - index));
      return DailyStats(
        date: date,
        totalCount: 50 + (index * 20) + (index % 3 * 10),
        dhikrCounts: {
          'سُبْحَانَ اللَّهِ': 20 + (index * 5),
          'الْحَمْدُ لِلَّهِ': 15 + (index * 3),
          'اللَّهُ أَكْبَرُ': 15 + (index * 4),
        },
        timeSpent: Duration(minutes: 10 + (index * 2)),
      );
    });
  }

  void _initializeAchievements() {
    _achievements = [
      Achievement(
        id: 'first_dhikr',
        title: 'البداية المباركة',
        description: 'أول ذكر لك',
        icon: Icons.star,
        color: Colors.amber,
        targetCount: 1,
        isUnlocked: _totalCount >= 1,
      ),
      Achievement(
        id: 'hundred_dhikr',
        title: 'المئة الأولى',
        description: 'أكمل 100 ذكر',
        icon: Icons.looks_one,
        color: Colors.green,
        targetCount: 100,
        isUnlocked: _totalCount >= 100,
      ),
      Achievement(
        id: 'thousand_dhikr',
        title: 'الألف المباركة',
        description: 'أكمل 1000 ذكر',
        icon: Icons.military_tech,
        color: Colors.purple,
        targetCount: 1000,
        isUnlocked: _totalCount >= 1000,
      ),
      Achievement(
        id: 'daily_habit',
        title: 'عادة يومية',
        description: 'اذكر الله 7 أيام متتالية',
        icon: Icons.calendar_today,
        color: Colors.blue,
        targetCount: 7,
        isUnlocked: false, // Would need proper tracking
      ),
      Achievement(
        id: 'night_owl',
        title: 'قيام الليل',
        description: 'اذكر الله بعد منتصف الليل',
        icon: Icons.nightlight,
        color: Colors.indigo,
        targetCount: 1,
        isUnlocked: false,
      ),
      Achievement(
        id: 'early_bird',
        title: 'بركة الفجر',
        description: 'اذكر الله قبل الفجر',
        icon: Icons.wb_sunny,
        color: Colors.orange,
        targetCount: 1,
        isUnlocked: false,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5),
        appBar: AppBar(
          title: const Text('الإحصائيات والإنجازات'),
          backgroundColor: const Color(0xFF2E7D32),
          foregroundColor: Colors.white,
          elevation: 0,
          bottom: TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            tabs: const [
              Tab(text: 'الإحصائيات', icon: Icon(Icons.bar_chart)),
              Tab(text: 'الإنجازات', icon: Icon(Icons.emoji_events)),
              Tab(text: 'التقدم', icon: Icon(Icons.trending_up)),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildStatisticsTab(),
            _buildAchievementsTab(),
            _buildProgressTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsTab() {
    return AnimationLimiter(
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 375),
          childAnimationBuilder: (widget) => SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(child: widget),
          ),
          children: [
            _buildStatsOverview(),
            const SizedBox(height: 20),
            _buildWeeklyChart(),
            const SizedBox(height: 20),
            _buildDhikrBreakdown(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsOverview() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نظرة عامة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2E7D32),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'المجموع الكلي',
                    _totalCount.toString(),
                    Icons.all_inclusive,
                    Colors.purple,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'اليوم',
                    _todayCount.toString(),
                    Icons.today,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'هذا الأسبوع',
                    _weekCount.toString(),
                    Icons.date_range,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'هذا الشهر',
                    _monthCount.toString(),
                    Icons.calendar_month,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildWeeklyChart() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الأسبوع الماضي',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2E7D32),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: StatsChart(weeklyStats: _weeklyStats),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDhikrBreakdown() {
    final dhikrTypes = [
      {'name': 'سُبْحَانَ اللَّهِ', 'count': 150, 'color': Colors.green},
      {'name': 'الْحَمْدُ لِلَّهِ', 'count': 120, 'color': Colors.blue},
      {'name': 'اللَّهُ أَكْبَرُ', 'count': 100, 'color': Colors.orange},
      {'name': 'لَا إِلَهَ إِلَّا اللَّهُ', 'count': 80, 'color': Colors.purple},
    ];

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفصيل الأذكار',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2E7D32),
              ),
            ),
            const SizedBox(height: 16),
            ...dhikrTypes.map((dhikr) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: dhikr['color'] as Color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      dhikr['name'] as String,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                  Text(
                    '${dhikr['count']}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: dhikr['color'] as Color,
                    ),
                  ),
                ],
              ),
            )).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementsTab() {
    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _achievements.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 375),
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: AchievementCard(
                  achievement: _achievements[index],
                  currentProgress: _totalCount,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildProgressTab() {
    return AnimationLimiter(
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 375),
          childAnimationBuilder: (widget) => SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(child: widget),
          ),
          children: [
            _buildProgressCard(
              'الهدف اليومي',
              _todayCount,
              100,
              Colors.green,
              Icons.today,
            ),
            const SizedBox(height: 16),
            _buildProgressCard(
              'الهدف الأسبوعي',
              _weekCount,
              500,
              Colors.blue,
              Icons.date_range,
            ),
            const SizedBox(height: 16),
            _buildProgressCard(
              'الهدف الشهري',
              _monthCount,
              2000,
              Colors.purple,
              Icons.calendar_month,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressCard(
    String title,
    int current,
    int target,
    Color color,
    IconData icon,
  ) {
    final progress = current / target;
    final percentage = (progress * 100).clamp(0, 100).toInt();

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '$current / $target',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '$percentage%',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: progress.clamp(0.0, 1.0),
              backgroundColor: color.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 8,
            ),
          ],
        ),
      ),
    );
  }
}
