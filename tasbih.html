<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المسبحة الذكية</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Arial', sans-serif;
            background: linear-gradient(135deg, #0f4c3a 0%, #2d5a27 25%, #1a472a 50%, #0d3d2a 75%, #0a2f1f 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 219, 226, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 206, 84, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 226, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .islamic-pattern {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25px 25px, rgba(255,255,255,0.03) 2px, transparent 2px),
                radial-gradient(circle at 75px 75px, rgba(255,255,255,0.02) 1px, transparent 1px);
            background-size: 100px 100px, 150px 150px;
            animation: patternMove 60s linear infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes patternMove {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(100px) translateY(100px); }
        }

        .container {
            text-align: center;
            z-index: 10;
            padding: 20px;
            max-width: 500px;
            width: 100%;
        }

        .app-title {
            font-family: 'Amiri', serif;
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 40px;
            background: linear-gradient(135deg, #ffd700, #ffed4e, #fff59d);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            position: relative;
        }

        .app-title::after {
            content: '✨';
            position: absolute;
            top: -10px;
            right: -30px;
            font-size: 1.5rem;
            animation: sparkle 2s ease-in-out infinite;
        }

        @keyframes sparkle {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .dhikr-text {
            font-family: 'Amiri', serif;
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 30px;
            padding: 25px 30px;
            background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05));
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255,255,255,0.2);
            box-shadow:
                0 8px 32px rgba(0,0,0,0.1),
                inset 0 1px 0 rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }

        .dhikr-text::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .counter {
            font-size: 4.5rem;
            font-weight: 700;
            margin-bottom: 40px;
            padding: 40px;
            background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            border-radius: 50%;
            width: 220px;
            height: 220px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 40px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow:
                0 20px 40px rgba(0,0,0,0.2),
                0 0 0 1px rgba(255,255,255,0.1),
                inset 0 1px 0 rgba(255,255,255,0.2);
            position: relative;
        }

        .counter::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ffd700, #ffed4e, #fff59d, #ffd700);
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .counter.pulse {
            transform: scale(1.1);
            box-shadow:
                0 25px 50px rgba(0,0,0,0.3),
                0 0 0 1px rgba(255,255,255,0.2);
        }

        .counter.pulse::before {
            opacity: 0.3;
        }

        .tasbih-button {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border: none;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d5a27;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow:
                0 15px 35px rgba(0,0,0,0.15),
                0 5px 15px rgba(0,0,0,0.1),
                inset 0 1px 0 rgba(255,255,255,0.8);
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .tasbih-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(45,90,39,0.1), rgba(45,90,39,0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .tasbih-button:hover {
            transform: translateY(-8px) scale(1.05);
            box-shadow:
                0 25px 50px rgba(0,0,0,0.2),
                0 10px 25px rgba(0,0,0,0.15),
                inset 0 1px 0 rgba(255,255,255,0.9);
        }

        .tasbih-button:hover::before {
            opacity: 1;
        }

        .tasbih-button:active {
            transform: translateY(-4px) scale(0.98);
            transition: all 0.1s ease;
        }

        .tasbih-button .icon {
            font-size: 3.5rem;
            margin-bottom: 8px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        .tasbih-button .text {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            gap: 20px;
            margin-top: 30px;
        }

        .control-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: rgba(255,255,255,0.2);
            color: white;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .control-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .dhikr-selector {
            display: flex;
            gap: 12px;
            margin-top: 25px;
            flex-wrap: wrap;
            justify-content: center;
            padding: 0 20px;
        }

        .dhikr-option {
            padding: 12px 20px;
            border: 2px solid rgba(255,255,255,0.2);
            border-radius: 25px;
            background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            color: white;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            font-size: 0.95rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .dhikr-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s ease;
        }

        .dhikr-option:hover::before {
            left: 100%;
        }

        .dhikr-option.active {
            background: linear-gradient(135deg, rgba(255,215,0,0.3), rgba(255,215,0,0.2));
            border-color: rgba(255,215,0,0.5);
            color: #ffd700;
            box-shadow: 0 5px 15px rgba(255,215,0,0.2);
            transform: translateY(-2px);
        }

        .dhikr-option:hover {
            background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            border-color: rgba(255,255,255,0.4);
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }

        .progress-container {
            margin: 25px auto;
            width: 100%;
            max-width: 350px;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: rgba(255,255,255,0.15);
            border-radius: 20px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ffd700, #ffed4e, #fff59d);
            border-radius: 20px;
            transition: width 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow:
                0 0 20px rgba(255,215,0,0.4),
                inset 0 1px 0 rgba(255,255,255,0.3);
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: progressShine 2s infinite;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .milestone-text {
            font-size: 1rem;
            margin-top: 12px;
            opacity: 0.9;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .app-title { font-size: 2rem; }
            .dhikr-text { font-size: 1.5rem; }
            .counter { font-size: 3rem; width: 150px; height: 150px; }
            .tasbih-button { width: 140px; height: 140px; }
            .controls { flex-direction: column; align-items: center; }
        }
    </style>
</head>
<body>
    <div class="islamic-pattern"></div>

    <div class="container">
        <h1 class="app-title">المسبحة الذكية</h1>

        <div class="dhikr-text" id="dhikrText">سُبْحَانَ اللَّهِ</div>

        <div class="counter" id="counter">0</div>

        <button class="tasbih-button" id="tasbihButton">
            <div class="icon">🤲</div>
            <div class="text">اضغط للذكر</div>
        </button>

        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
            <div class="milestone-text" id="milestoneText">التقدم نحو 33: 0/33</div>
        </div>

        <div class="dhikr-selector">
            <div class="dhikr-option active" data-dhikr="سُبْحَانَ اللَّهِ">سبحان الله</div>
            <div class="dhikr-option" data-dhikr="الْحَمْدُ لِلَّهِ">الحمد لله</div>
            <div class="dhikr-option" data-dhikr="اللَّهُ أَكْبَرُ">الله أكبر</div>
            <div class="dhikr-option" data-dhikr="لَا إِلَهَ إِلَّا اللَّهُ">لا إله إلا الله</div>
            <div class="dhikr-option" data-dhikr="أَسْتَغْفِرُ اللَّهَ">أستغفر الله</div>
        </div>

        <div class="controls">
            <button class="control-btn" id="resetBtn">إعادة تعيين</button>
            <button class="control-btn" id="shareBtn">مشاركة</button>
        </div>
    </div>

    <script>
        let counter = 0;
        let currentDhikr = 'سُبْحَانَ اللَّهِ';
        
        const counterElement = document.getElementById('counter');
        const dhikrTextElement = document.getElementById('dhikrText');
        const tasbihButton = document.getElementById('tasbihButton');
        const resetBtn = document.getElementById('resetBtn');
        const shareBtn = document.getElementById('shareBtn');
        const progressFill = document.getElementById('progressFill');
        const milestoneText = document.getElementById('milestoneText');
        const dhikrOptions = document.querySelectorAll('.dhikr-option');

        // Load saved data
        function loadData() {
            const saved = localStorage.getItem('tasbih_data');
            if (saved) {
                const data = JSON.parse(saved);
                counter = data.counter || 0;
                currentDhikr = data.dhikr || 'سُبْحَانَ اللَّهِ';
                updateDisplay();
                updateDhikrSelection();
            }
        }

        // Save data
        function saveData() {
            const data = {
                counter: counter,
                dhikr: currentDhikr
            };
            localStorage.setItem('tasbih_data', JSON.stringify(data));
        }

        // Update display
        function updateDisplay() {
            counterElement.textContent = counter;
            dhikrTextElement.textContent = currentDhikr;
            
            // Update progress
            const progress = (counter % 33) / 33 * 100;
            progressFill.style.width = progress + '%';
            milestoneText.textContent = `التقدم نحو 33: ${counter % 33}/33`;
            
            // Pulse effect
            counterElement.classList.add('pulse');
            setTimeout(() => {
                counterElement.classList.remove('pulse');
            }, 300);
        }

        // Update dhikr selection
        function updateDhikrSelection() {
            dhikrOptions.forEach(option => {
                option.classList.remove('active');
                if (option.dataset.dhikr === currentDhikr) {
                    option.classList.add('active');
                }
            });
        }

        // Increment counter
        function incrementCounter() {
            counter++;
            updateDisplay();
            saveData();
            
            // Check milestones
            if (counter % 33 === 0) {
                setTimeout(() => {
                    alert(`مبارك! لقد أكملت ${counter} من الأذكار`);
                }, 300);
            }
        }

        // Reset counter
        function resetCounter() {
            if (confirm('هل تريد إعادة تعيين العداد؟')) {
                counter = 0;
                updateDisplay();
                saveData();
            }
        }

        // Share function
        function shareProgress() {
            const text = `لقد أكملت ${counter} من الأذكار في تطبيق المسبحة الذكية! 🕌`;
            if (navigator.share) {
                navigator.share({
                    title: 'المسبحة الذكية',
                    text: text
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                navigator.clipboard.writeText(text).then(() => {
                    alert('تم نسخ النص للحافظة!');
                });
            }
        }

        // Event listeners
        tasbihButton.addEventListener('click', incrementCounter);
        resetBtn.addEventListener('click', resetCounter);
        shareBtn.addEventListener('click', shareProgress);

        // Dhikr selection
        dhikrOptions.forEach(option => {
            option.addEventListener('click', () => {
                currentDhikr = option.dataset.dhikr;
                counter = 0; // Reset counter when changing dhikr
                updateDisplay();
                updateDhikrSelection();
                saveData();
            });
        });

        // Keyboard support
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                incrementCounter();
            } else if (e.code === 'KeyR') {
                resetCounter();
            }
        });

        // Initialize
        loadData();
        updateDisplay();
        updateDhikrSelection();

        // Add some visual effects
        setInterval(() => {
            const wave = document.querySelector('.wave');
            wave.style.opacity = Math.sin(Date.now() / 1000) * 0.1 + 0.1;
        }, 100);
    </script>
</body>
</html>
