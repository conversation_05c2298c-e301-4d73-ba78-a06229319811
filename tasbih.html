<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المسبحة الذكية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2E7D32, #4CAF50, #81C784);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            overflow: hidden;
        }

        .background-effects {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .wave {
            position: absolute;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateX(-50%) translateY(-50%) rotate(0deg); }
            100% { transform: translateX(-50%) translateY(-50%) rotate(360deg); }
        }

        .container {
            text-align: center;
            z-index: 1;
            padding: 20px;
        }

        .app-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .dhikr-text {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .counter {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 200px;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 40px;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .counter.pulse {
            transform: scale(1.1);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }

        .tasbih-button {
            width: 180px;
            height: 180px;
            border-radius: 50%;
            background: linear-gradient(145deg, #ffffff, #f0f0f0);
            border: none;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: bold;
            color: #2E7D32;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 30px;
        }

        .tasbih-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }

        .tasbih-button:active {
            transform: scale(0.95);
        }

        .tasbih-button .icon {
            font-size: 3rem;
            margin-bottom: 10px;
        }

        .controls {
            display: flex;
            gap: 20px;
            margin-top: 30px;
        }

        .control-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: rgba(255,255,255,0.2);
            color: white;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .control-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .dhikr-selector {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .dhikr-option {
            padding: 8px 16px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            background: rgba(255,255,255,0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .dhikr-option.active {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.6);
        }

        .dhikr-option:hover {
            background: rgba(255,255,255,0.2);
        }

        .progress-bar {
            width: 300px;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            margin: 20px auto;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #FFD700, #FFA500);
            border-radius: 4px;
            transition: width 0.3s ease;
            box-shadow: 0 0 10px rgba(255,215,0,0.5);
        }

        .milestone-text {
            font-size: 0.9rem;
            margin-top: 10px;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .app-title { font-size: 2rem; }
            .dhikr-text { font-size: 1.5rem; }
            .counter { font-size: 3rem; width: 150px; height: 150px; }
            .tasbih-button { width: 140px; height: 140px; }
            .controls { flex-direction: column; align-items: center; }
        }
    </style>
</head>
<body>
    <div class="background-effects">
        <div class="wave"></div>
    </div>

    <div class="container">
        <h1 class="app-title">المسبحة الذكية</h1>
        
        <div class="dhikr-text" id="dhikrText">سُبْحَانَ اللَّهِ</div>
        
        <div class="counter" id="counter">0</div>
        
        <button class="tasbih-button" id="tasbihButton">
            <div class="icon">👆</div>
            <div>اضغط للذكر</div>
        </button>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
        </div>
        <div class="milestone-text" id="milestoneText">التقدم نحو 33: 0/33</div>

        <div class="dhikr-selector">
            <div class="dhikr-option active" data-dhikr="سُبْحَانَ اللَّهِ">سبحان الله</div>
            <div class="dhikr-option" data-dhikr="الْحَمْدُ لِلَّهِ">الحمد لله</div>
            <div class="dhikr-option" data-dhikr="اللَّهُ أَكْبَرُ">الله أكبر</div>
            <div class="dhikr-option" data-dhikr="لَا إِلَهَ إِلَّا اللَّهُ">لا إله إلا الله</div>
            <div class="dhikr-option" data-dhikr="أَسْتَغْفِرُ اللَّهَ">أستغفر الله</div>
        </div>

        <div class="controls">
            <button class="control-btn" id="resetBtn">إعادة تعيين</button>
            <button class="control-btn" id="shareBtn">مشاركة</button>
        </div>
    </div>

    <script>
        let counter = 0;
        let currentDhikr = 'سُبْحَانَ اللَّهِ';
        
        const counterElement = document.getElementById('counter');
        const dhikrTextElement = document.getElementById('dhikrText');
        const tasbihButton = document.getElementById('tasbihButton');
        const resetBtn = document.getElementById('resetBtn');
        const shareBtn = document.getElementById('shareBtn');
        const progressFill = document.getElementById('progressFill');
        const milestoneText = document.getElementById('milestoneText');
        const dhikrOptions = document.querySelectorAll('.dhikr-option');

        // Load saved data
        function loadData() {
            const saved = localStorage.getItem('tasbih_data');
            if (saved) {
                const data = JSON.parse(saved);
                counter = data.counter || 0;
                currentDhikr = data.dhikr || 'سُبْحَانَ اللَّهِ';
                updateDisplay();
                updateDhikrSelection();
            }
        }

        // Save data
        function saveData() {
            const data = {
                counter: counter,
                dhikr: currentDhikr
            };
            localStorage.setItem('tasbih_data', JSON.stringify(data));
        }

        // Update display
        function updateDisplay() {
            counterElement.textContent = counter;
            dhikrTextElement.textContent = currentDhikr;
            
            // Update progress
            const progress = (counter % 33) / 33 * 100;
            progressFill.style.width = progress + '%';
            milestoneText.textContent = `التقدم نحو 33: ${counter % 33}/33`;
            
            // Pulse effect
            counterElement.classList.add('pulse');
            setTimeout(() => {
                counterElement.classList.remove('pulse');
            }, 300);
        }

        // Update dhikr selection
        function updateDhikrSelection() {
            dhikrOptions.forEach(option => {
                option.classList.remove('active');
                if (option.dataset.dhikr === currentDhikr) {
                    option.classList.add('active');
                }
            });
        }

        // Increment counter
        function incrementCounter() {
            counter++;
            updateDisplay();
            saveData();
            
            // Check milestones
            if (counter % 33 === 0) {
                setTimeout(() => {
                    alert(`مبارك! لقد أكملت ${counter} من الأذكار`);
                }, 300);
            }
        }

        // Reset counter
        function resetCounter() {
            if (confirm('هل تريد إعادة تعيين العداد؟')) {
                counter = 0;
                updateDisplay();
                saveData();
            }
        }

        // Share function
        function shareProgress() {
            const text = `لقد أكملت ${counter} من الأذكار في تطبيق المسبحة الذكية! 🕌`;
            if (navigator.share) {
                navigator.share({
                    title: 'المسبحة الذكية',
                    text: text
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                navigator.clipboard.writeText(text).then(() => {
                    alert('تم نسخ النص للحافظة!');
                });
            }
        }

        // Event listeners
        tasbihButton.addEventListener('click', incrementCounter);
        resetBtn.addEventListener('click', resetCounter);
        shareBtn.addEventListener('click', shareProgress);

        // Dhikr selection
        dhikrOptions.forEach(option => {
            option.addEventListener('click', () => {
                currentDhikr = option.dataset.dhikr;
                counter = 0; // Reset counter when changing dhikr
                updateDisplay();
                updateDhikrSelection();
                saveData();
            });
        });

        // Keyboard support
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                incrementCounter();
            } else if (e.code === 'KeyR') {
                resetCounter();
            }
        });

        // Initialize
        loadData();
        updateDisplay();
        updateDhikrSelection();

        // Add some visual effects
        setInterval(() => {
            const wave = document.querySelector('.wave');
            wave.style.opacity = Math.sin(Date.now() / 1000) * 0.1 + 0.1;
        }, 100);
    </script>
</body>
</html>
