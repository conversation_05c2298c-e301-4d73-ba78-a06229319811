import 'package:flutter/material.dart';
import 'dart:math' as math;

class Dhikr<PERSON>utton extends StatefulWidget {
  final VoidCallback onPressed;
  final Color color;
  final double size;

  const DhikrButton({
    super.key,
    required this.onPressed,
    required this.color,
    this.size = 200,
  });

  @override
  State<DhikrButton> createState() => _DhikrButtonState();
}

class _DhikrButtonState extends State<DhikrButton>
    with TickerProviderStateMixin {
  late AnimationController _pressController;
  late AnimationController _rippleController;
  late AnimationController _glowController;
  
  late Animation<double> _pressAnimation;
  late Animation<double> _rippleAnimation;
  late Animation<double> _glowAnimation;

  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    
    _pressController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _glowController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pressAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _pressController,
      curve: Curves.easeInOut,
    ));

    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rippleController,
      curve: Curves.easeOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    _glowController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pressController.dispose();
    _rippleController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() {
      _isPressed = true;
    });
    _pressController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() {
      _isPressed = false;
    });
    _pressController.reverse();
    _rippleController.forward().then((_) {
      _rippleController.reset();
    });
    widget.onPressed();
  }

  void _handleTapCancel() {
    setState(() {
      _isPressed = false;
    });
    _pressController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _pressAnimation,
          _rippleAnimation,
          _glowAnimation,
        ]),
        builder: (context, child) {
          return Transform.scale(
            scale: _pressAnimation.value,
            child: CustomPaint(
              painter: DhikrButtonPainter(
                color: widget.color,
                rippleProgress: _rippleAnimation.value,
                glowProgress: _glowAnimation.value,
                isPressed: _isPressed,
              ),
              child: Container(
                width: widget.size,
                height: widget.size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      widget.color.withOpacity(0.8),
                      widget.color,
                      widget.color.withOpacity(0.9),
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: widget.color.withOpacity(0.4),
                      blurRadius: _isPressed ? 15 : 25,
                      spreadRadius: _isPressed ? 2 : 5,
                      offset: const Offset(0, 8),
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.touch_app,
                        size: widget.size * 0.25,
                        color: Colors.white,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'اضغط للذكر',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: widget.size * 0.08,
                          fontWeight: FontWeight.bold,
                          shadows: const [
                            Shadow(
                              offset: Offset(1, 1),
                              blurRadius: 2,
                              color: Colors.black26,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

class DhikrButtonPainter extends CustomPainter {
  final Color color;
  final double rippleProgress;
  final double glowProgress;
  final bool isPressed;

  DhikrButtonPainter({
    required this.color,
    required this.rippleProgress,
    required this.glowProgress,
    required this.isPressed,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Draw glow effect
    if (!isPressed) {
      final glowRadius = radius + (20 * glowProgress);
      final glowPaint = Paint()
        ..color = color.withOpacity(0.3 * (1 - glowProgress))
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 10);
      
      canvas.drawCircle(center, glowRadius, glowPaint);
    }

    // Draw ripple effect
    if (rippleProgress > 0) {
      for (int i = 0; i < 3; i++) {
        final rippleRadius = radius + (50 * rippleProgress) - (i * 15);
        if (rippleRadius > radius) {
          final ripplePaint = Paint()
            ..color = color.withOpacity(0.3 * (1 - rippleProgress))
            ..style = PaintingStyle.stroke
            ..strokeWidth = 3;
          
          canvas.drawCircle(center, rippleRadius, ripplePaint);
        }
      }
    }

    // Draw particle effects
    _drawParticles(canvas, center, radius);
  }

  void _drawParticles(Canvas canvas, Offset center, double radius) {
    final particlePaint = Paint()
      ..color = Colors.white.withOpacity(0.6)
      ..style = PaintingStyle.fill;

    for (int i = 0; i < 8; i++) {
      final angle = (i * math.pi * 2) / 8;
      final particleRadius = radius + 30 + (10 * math.sin(glowProgress * math.pi * 2));
      final particleOffset = Offset(
        center.dx + math.cos(angle) * particleRadius,
        center.dy + math.sin(angle) * particleRadius,
      );
      
      canvas.drawCircle(particleOffset, 2, particlePaint);
    }
  }

  @override
  bool shouldRepaint(DhikrButtonPainter oldDelegate) {
    return rippleProgress != oldDelegate.rippleProgress ||
           glowProgress != oldDelegate.glowProgress ||
           isPressed != oldDelegate.isPressed;
  }
}

class FloatingParticle extends StatefulWidget {
  final Color color;
  final double size;
  final Duration duration;

  const FloatingParticle({
    super.key,
    required this.color,
    this.size = 4,
    this.duration = const Duration(seconds: 3),
  });

  @override
  State<FloatingParticle> createState() => _FloatingParticleState();
}

class _FloatingParticleState extends State<FloatingParticle>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _floatAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _floatAnimation = Tween<double>(
      begin: 0,
      end: -100,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.7, 1.0),
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _floatAnimation.value),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                color: widget.color,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: widget.color.withOpacity(0.5),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
