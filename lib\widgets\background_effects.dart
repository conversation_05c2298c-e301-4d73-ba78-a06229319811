import 'package:flutter/material.dart';
import 'dart:math' as math;

class BackgroundEffects extends StatefulWidget {
  final Color primaryColor;
  final Color secondaryColor;
  final bool isAnimating;

  const BackgroundEffects({
    super.key,
    required this.primaryColor,
    required this.secondaryColor,
    this.isAnimating = false,
  });

  @override
  State<BackgroundEffects> createState() => _BackgroundEffectsState();
}

class _BackgroundEffectsState extends State<BackgroundEffects>
    with TickerProviderStateMixin {
  late AnimationController _waveController;
  late AnimationController _particleController;
  late AnimationController _geometryController;

  late Animation<double> _waveAnimation;
  late Animation<double> _particleAnimation;
  late Animation<double> _geometryAnimation;

  @override
  void initState() {
    super.initState();
    
    _waveController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );
    
    _particleController = AnimationController(
      duration: const Duration(seconds: 6),
      vsync: this,
    );
    
    _geometryController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    _waveAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(_waveController);

    _particleAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(_particleController);

    _geometryAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(_geometryController);

    _waveController.repeat();
    _particleController.repeat();
    _geometryController.repeat();
  }

  @override
  void dispose() {
    _waveController.dispose();
    _particleController.dispose();
    _geometryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Animated waves
        AnimatedBuilder(
          animation: _waveAnimation,
          builder: (context, child) {
            return CustomPaint(
              painter: WavePainter(
                animation: _waveAnimation.value,
                primaryColor: widget.primaryColor,
                secondaryColor: widget.secondaryColor,
              ),
              size: Size.infinite,
            );
          },
        ),
        
        // Floating particles
        AnimatedBuilder(
          animation: _particleAnimation,
          builder: (context, child) {
            return CustomPaint(
              painter: ParticlePainter(
                animation: _particleAnimation.value,
                color: widget.primaryColor,
                isAnimating: widget.isAnimating,
              ),
              size: Size.infinite,
            );
          },
        ),
        
        // Geometric patterns
        AnimatedBuilder(
          animation: _geometryAnimation,
          builder: (context, child) {
            return CustomPaint(
              painter: GeometryPainter(
                animation: _geometryAnimation.value,
                color: widget.secondaryColor,
              ),
              size: Size.infinite,
            );
          },
        ),
      ],
    );
  }
}

class WavePainter extends CustomPainter {
  final double animation;
  final Color primaryColor;
  final Color secondaryColor;

  WavePainter({
    required this.animation,
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint1 = Paint()
      ..color = primaryColor.withOpacity(0.1)
      ..style = PaintingStyle.fill;

    final paint2 = Paint()
      ..color = secondaryColor.withOpacity(0.05)
      ..style = PaintingStyle.fill;

    // Draw multiple wave layers
    _drawWave(canvas, size, paint1, animation, 0.8, 60);
    _drawWave(canvas, size, paint2, animation + math.pi, 0.6, 40);
    _drawWave(canvas, size, paint1, animation + math.pi / 2, 0.4, 80);
  }

  void _drawWave(Canvas canvas, Size size, Paint paint, double phase, 
                 double amplitude, double frequency) {
    final path = Path();
    final waveHeight = size.height * amplitude;
    
    path.moveTo(0, size.height);
    
    for (double x = 0; x <= size.width; x += 2) {
      final y = size.height - waveHeight * 
          math.sin((x / size.width * frequency) + phase);
      path.lineTo(x, y);
    }
    
    path.lineTo(size.width, size.height);
    path.close();
    
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(WavePainter oldDelegate) {
    return animation != oldDelegate.animation;
  }
}

class ParticlePainter extends CustomPainter {
  final double animation;
  final Color color;
  final bool isAnimating;

  ParticlePainter({
    required this.animation,
    required this.color,
    required this.isAnimating,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    final glowPaint = Paint()
      ..color = color.withOpacity(0.1)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5);

    // Generate particles
    final random = math.Random(42); // Fixed seed for consistent particles
    
    for (int i = 0; i < 20; i++) {
      final baseX = random.nextDouble() * size.width;
      final baseY = random.nextDouble() * size.height;
      
      // Animate particle position
      final offsetX = math.sin(animation * 2 + i) * 20;
      final offsetY = math.cos(animation * 1.5 + i) * 15;
      
      final x = baseX + offsetX;
      final y = baseY + offsetY;
      
      final particleSize = 2 + math.sin(animation * 3 + i) * 2;
      
      // Draw glow
      canvas.drawCircle(Offset(x, y), particleSize * 2, glowPaint);
      
      // Draw particle
      canvas.drawCircle(Offset(x, y), particleSize, paint);
      
      // Add extra effects when animating
      if (isAnimating) {
        final burstPaint = Paint()
          ..color = color.withOpacity(0.6)
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(Offset(x, y), particleSize * 1.5, burstPaint);
      }
    }
  }

  @override
  bool shouldRepaint(ParticlePainter oldDelegate) {
    return animation != oldDelegate.animation || 
           isAnimating != oldDelegate.isAnimating;
  }
}

class GeometryPainter extends CustomPainter {
  final double animation;
  final Color color;

  GeometryPainter({
    required this.animation,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withOpacity(0.08)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // Draw rotating geometric patterns
    canvas.save();
    canvas.translate(size.width / 2, size.height / 2);
    canvas.rotate(animation);

    // Draw hexagons
    for (int i = 0; i < 3; i++) {
      final radius = 50.0 + (i * 30);
      _drawHexagon(canvas, paint, radius);
    }

    // Draw connecting lines
    final linePaint = Paint()
      ..color = color.withOpacity(0.05)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    for (int i = 0; i < 6; i++) {
      final angle = (i * math.pi * 2) / 6;
      final startRadius = 50.0;
      final endRadius = 110.0;
      
      final startX = math.cos(angle) * startRadius;
      final startY = math.sin(angle) * startRadius;
      final endX = math.cos(angle) * endRadius;
      final endY = math.sin(angle) * endRadius;
      
      canvas.drawLine(
        Offset(startX, startY),
        Offset(endX, endY),
        linePaint,
      );
    }

    canvas.restore();
  }

  void _drawHexagon(Canvas canvas, Paint paint, double radius) {
    final path = Path();
    
    for (int i = 0; i < 6; i++) {
      final angle = (i * math.pi * 2) / 6;
      final x = math.cos(angle) * radius;
      final y = math.sin(angle) * radius;
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(GeometryPainter oldDelegate) {
    return animation != oldDelegate.animation;
  }
}

class IslamicPattern extends StatefulWidget {
  final Color color;
  final double size;

  const IslamicPattern({
    super.key,
    required this.color,
    this.size = 100,
  });

  @override
  State<IslamicPattern> createState() => _IslamicPatternState();
}

class _IslamicPatternState extends State<IslamicPattern>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );
    
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(_controller);

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _rotationAnimation,
      builder: (context, child) {
        return CustomPaint(
          painter: IslamicPatternPainter(
            color: widget.color,
            rotation: _rotationAnimation.value,
          ),
          size: Size(widget.size, widget.size),
        );
      },
    );
  }
}

class IslamicPatternPainter extends CustomPainter {
  final Color color;
  final double rotation;

  IslamicPatternPainter({
    required this.color,
    required this.rotation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 3;

    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);

    // Draw 8-pointed star pattern
    for (int i = 0; i < 8; i++) {
      final angle = (i * math.pi * 2) / 8;
      final outerRadius = radius;
      final innerRadius = radius * 0.6;
      
      final outerX = math.cos(angle) * outerRadius;
      final outerY = math.sin(angle) * outerRadius;
      
      final innerAngle = angle + (math.pi / 8);
      final innerX = math.cos(innerAngle) * innerRadius;
      final innerY = math.sin(innerAngle) * innerRadius;
      
      canvas.drawLine(Offset(0, 0), Offset(outerX, outerY), paint);
      canvas.drawLine(Offset(outerX, outerY), Offset(innerX, innerY), paint);
    }

    canvas.restore();
  }

  @override
  bool shouldRepaint(IslamicPatternPainter oldDelegate) {
    return rotation != oldDelegate.rotation;
  }
}
