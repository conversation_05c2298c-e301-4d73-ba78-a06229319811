import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:vibration/vibration.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/dhikr_model.dart';
import '../widgets/animated_counter.dart';
import '../widgets/dhikr_button.dart';
import '../widgets/background_effects.dart';
import 'statistics_screen.dart';

class TasbihScreen extends StatefulWidget {
  const TasbihScreen({super.key});

  @override
  State<TasbihScreen> createState() => _TasbihScreenState();
}

class _TasbihScreenState extends State<TasbihScreen>
    with TickerProviderStateMixin {
  int _counter = 0;
  int _totalCounter = 0;
  int _selectedDhikrIndex = 0;
  
  late AnimationController _pulseController;
  late AnimationController _backgroundController;
  late Animation<double> _pulseAnimation;
  late Animation<Color?> _colorAnimation;

  final List<DhikrModel> _dhikrList = [
    DhikrModel(
      text: 'سُبْحَانَ اللَّهِ',
      translation: 'Glory be to Allah',
      color: const Color(0xFF4CAF50),
      particleColor: Colors.green,
    ),
    DhikrModel(
      text: 'الْحَمْدُ لِلَّهِ',
      translation: 'Praise be to Allah',
      color: const Color(0xFF2196F3),
      particleColor: Colors.blue,
    ),
    DhikrModel(
      text: 'اللَّهُ أَكْبَرُ',
      translation: 'Allah is Greatest',
      color: const Color(0xFFFF9800),
      particleColor: Colors.orange,
    ),
    DhikrModel(
      text: 'لَا إِلَهَ إِلَّا اللَّهُ',
      translation: 'There is no god but Allah',
      color: const Color(0xFF9C27B0),
      particleColor: Colors.purple,
    ),
    DhikrModel(
      text: 'أَسْتَغْفِرُ اللَّهَ',
      translation: 'I seek forgiveness from Allah',
      color: const Color(0xFFE91E63),
      particleColor: Colors.pink,
    ),
    DhikrModel(
      text: 'صَلَّى اللَّهُ عَلَيْهِ وَسَلَّمَ',
      translation: 'Peace and blessings upon him',
      color: const Color(0xFF795548),
      particleColor: Colors.brown,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadCounters();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.elasticOut,
    ));

    _colorAnimation = ColorTween(
      begin: _dhikrList[_selectedDhikrIndex].color,
      end: _dhikrList[_selectedDhikrIndex].color.withOpacity(0.3),
    ).animate(_backgroundController);

    _backgroundController.repeat(reverse: true);
  }

  Future<void> _loadCounters() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _counter = prefs.getInt('counter_${_selectedDhikrIndex}') ?? 0;
      _totalCounter = prefs.getInt('total_counter') ?? 0;
    });
  }

  Future<void> _saveCounters() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('counter_${_selectedDhikrIndex}', _counter);
    await prefs.setInt('total_counter', _totalCounter);
  }

  void _incrementCounter() async {
    // Haptic feedback
    if (await Vibration.hasVibrator() ?? false) {
      Vibration.vibrate(duration: 50);
    }
    
    // System sound
    SystemSound.play(SystemSoundType.click);

    setState(() {
      _counter++;
      _totalCounter++;
    });

    // Trigger animations
    _pulseController.forward().then((_) {
      _pulseController.reverse();
    });

    // Save counters
    _saveCounters();

    // Check for milestones
    _checkMilestones();
  }

  void _checkMilestones() {
    if (_counter % 33 == 0 && _counter > 0) {
      _showMilestoneDialog('تهانينا!', 'لقد أكملت ${_counter} من الأذكار');
    } else if (_counter % 100 == 0 && _counter > 0) {
      _showMilestoneDialog('إنجاز رائع!', 'لقد وصلت إلى ${_counter} ذكر');
    }
  }

  void _showMilestoneDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title, textAlign: TextAlign.center),
        content: Text(message, textAlign: TextAlign.center),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('الحمد لله'),
          ),
        ],
      ),
    );
  }

  void _resetCounter() {
    setState(() {
      _counter = 0;
    });
    _saveCounters();
  }

  void _changeDhikr(int index) {
    setState(() {
      _selectedDhikrIndex = index;
      _colorAnimation = ColorTween(
        begin: _dhikrList[index].color,
        end: _dhikrList[index].color.withOpacity(0.3),
      ).animate(_backgroundController);
    });
    _loadCounters();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: AnimatedBuilder(
          animation: _colorAnimation,
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    _colorAnimation.value ?? _dhikrList[_selectedDhikrIndex].color,
                    _dhikrList[_selectedDhikrIndex].color.withOpacity(0.1),
                    Colors.white,
                  ],
                ),
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    _buildAppBar(),
                    Expanded(
                      child: _buildMainContent(),
                    ),
                    _buildBottomNavigation(),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () {
              // Navigate to settings - placeholder for now
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('الإعدادات قريباً')),
              );
            },
            icon: const Icon(Icons.settings, color: Colors.white),
          ),
          const Text(
            'المسبحة الذكية',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const StatisticsScreen(),
                ),
              );
            },
            icon: const Icon(Icons.bar_chart, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return AnimationLimiter(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 375),
          childAnimationBuilder: (widget) => SlideAnimation(
            horizontalOffset: 50.0,
            child: FadeInAnimation(child: widget),
          ),
          children: [
            _buildDhikrText(),
            _buildCounter(),
            _buildTasbihButton(),
            _buildDhikrSelector(),
          ],
        ),
      ),
    );
  }

  Widget _buildDhikrText() {
    final currentDhikr = _dhikrList[_selectedDhikrIndex];
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Column(
        children: [
          Text(
            currentDhikr.text,
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              shadows: [
                Shadow(
                  offset: Offset(2, 2),
                  blurRadius: 4,
                  color: Colors.black26,
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            currentDhikr.translation,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCounter() {
    return AnimatedCounter(
      count: _counter,
      color: Colors.white,
    );
  }

  Widget _buildTasbihButton() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: DhikrButton(
            onPressed: _incrementCounter,
            color: _dhikrList[_selectedDhikrIndex].color,
          ),
        );
      },
    );
  }

  Widget _buildDhikrSelector() {
    return Container(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _dhikrList.length,
        itemBuilder: (context, index) {
          final isSelected = index == _selectedDhikrIndex;
          return GestureDetector(
            onTap: () => _changeDhikr(index),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: const EdgeInsets.symmetric(horizontal: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isSelected 
                    ? Colors.white 
                    : Colors.white.withOpacity(0.3),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected 
                      ? _dhikrList[index].color 
                      : Colors.transparent,
                  width: 2,
                ),
              ),
              child: Center(
                child: Text(
                  _dhikrList[index].text.split(' ')[0],
                  style: TextStyle(
                    color: isSelected 
                        ? _dhikrList[index].color 
                        : Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildBottomButton(
            icon: Icons.refresh,
            label: 'إعادة تعيين',
            onPressed: _resetCounter,
          ),
          _buildBottomButton(
            icon: Icons.share,
            label: 'مشاركة',
            onPressed: () {
              // Share functionality
            },
          ),
          _buildBottomButton(
            icon: Icons.bookmark,
            label: 'الإنجازات',
            onPressed: () {
              // Navigate to achievements
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: onPressed,
          icon: Icon(icon, color: Colors.white),
          style: IconButton.styleFrom(
            backgroundColor: Colors.white.withOpacity(0.2),
            padding: const EdgeInsets.all(12),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}
